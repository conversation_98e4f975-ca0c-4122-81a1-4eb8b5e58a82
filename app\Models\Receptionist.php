<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Receptionist extends Model
{
    use HasFactory;

    protected $fillable = [
        'department_id',
        'full_name',
        'unique_url',
        'qr_code_path',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($receptionist) {
            if (empty($receptionist->unique_url)) {
                $receptionist->unique_url = Str::random(8);
            }
        });
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function getLandingUrlAttribute(): string
    {
        return url("/landing/{$this->unique_url}");
    }

    public function visits(): HasMany
    {
        return $this->hasMany(Visit::class);
    }

    public function clicks(): HasMany
    {
        return $this->hasMany(Click::class);
    }
}
