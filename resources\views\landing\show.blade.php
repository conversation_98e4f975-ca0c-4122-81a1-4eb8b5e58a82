<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $receptionist->full_name }} - Spin & Win!</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Spin Wheel Implementation -->
    
    <style>
        .spin-button {
            transition: all 0.3s ease;
        }
        .spin-button:hover {
            transform: scale(1.05);
        }
        .platform-button {
            transition: all 0.3s ease;
        }
        .platform-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <!-- Logo -->
            @php
                $brandLogo = \App\Models\Setting::get('brand_logo');
                $logoUrl = null;
                if ($brandLogo) {
                    if (str_starts_with($brandLogo, 'assets/')) {
                        $logoUrl = \Illuminate\Support\Facades\Storage::disk('public')->exists($brandLogo)
                            ? \Illuminate\Support\Facades\Storage::disk('public')->url($brandLogo)
                            : asset($brandLogo);
                    } else {
                        $logoUrl = asset($brandLogo);
                    }
                }
            @endphp

            @if($logoUrl)
                <div class="mb-6">
                    <img src="{{ $logoUrl }}" alt="Logo" class="mx-auto h-16 w-auto">
                </div>
            @endif

            <h1 class="text-4xl font-bold text-gray-800 mb-2">Welcome!</h1>
            <p class="text-xl text-gray-600">{{ $receptionist->full_name }}</p>
            <p class="text-lg text-gray-500">{{ $receptionist->department->name }}</p>
        </div>

        @if($receptionist->department->gifts->count() > 0)
        <!-- Spin Wheel Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-6">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Spin the Wheel & Win Amazing Gifts!</h2>
                <p class="text-gray-600">Click the button below to spin and discover your prize</p>
            </div>

            <!-- Wheel Container -->
            <div class="flex justify-center mb-6">
                <div class="relative">
                    <canvas id="canvas" width="400" height="400"></canvas>
                </div>
            </div>

            <!-- Spin Button -->
            <div class="text-center mb-6">
                <button id="spin-button" class="spin-button bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-full text-xl font-bold shadow-lg hover:shadow-xl">
                    🎯 SPIN TO WIN!
                </button>
            </div>

            <!-- Result Display -->
            <div id="result-section" class="hidden">
                <div class="bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-6 text-center">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">🎉 Congratulations!</h3>
                    <p class="text-xl text-gray-700 mb-4">You won: <span id="prize-name" class="font-bold text-purple-600"></span></p>
                    <p class="text-gray-600">To claim your prize, please leave us a review on one of the platforms below:</p>
                </div>
            </div>
        </div>
        @else
        <!-- No Gifts - Direct to Reviews Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-800">Thank You for Visiting!</h2>
                <p class="text-gray-600 mb-6">We'd love to hear about your experience with us. Please leave us a review on one of the platforms below:</p>
                <div class="inline-flex items-center justify-center from-purple-100 to-pink-100 rounded-full">
                    <span class="text-2xl">⭐⭐⭐⭐⭐</span>
                </div>
            </div>
        </div>
        @endif

        <!-- Review Platforms Section -->
        <div id="platforms-section" class="{{ $receptionist->department->gifts->count() > 0 ? 'hidden' : '' }}">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <div class="text-center mb-6">
                    @if($receptionist->department->gifts->count() > 0)
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">📝 Claim Your Prize</h3>
                        <p class="text-gray-600">Leave a review on any of these platforms to claim your gift:</p>
                    @else
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">📝 Share Your Experience</h3>
                        <p class="text-gray-600">Leave a review on any of these platforms:</p>
                    @endif
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($platforms as $platform)
                        <button onclick="trackClick({{ $platform->id }})"
                                class="platform-button bg-white border-2 border-gray-200 rounded-xl p-6 text-center hover:border-purple-300 hover:bg-purple-50">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gray-200 rounded-full flex items-center justify-center">
                                <span class="text-xl">🌐</span>
                            </div>
                            <h4 class="font-bold text-gray-800 mb-2">{{ $platform->name }}</h4>
                            <p class="text-sm text-gray-600">Click to leave a review</p>
                        </button>
                    @endforeach
                </div>

                @if($receptionist->department->gifts->count() > 0)
                <div class="text-center mt-6">
                    <p class="text-sm text-gray-500">
                        After leaving your review, please show this page to our staff to claim your prize.
                    </p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <script>
        // Custom Spin Wheel Implementation
        (function() {
            const gifts = @json($receptionist->department->gifts);
            console.log('Gifts data:', gifts);

            // Check if we have gifts
            if (!gifts || gifts.length === 0) {
                console.log('No gifts found for this department - showing platforms directly');
                // If there are no gifts, the platforms section is already visible
                // No need to initialize the wheel
                return;
            }

        // Canvas and wheel setup
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = 180;

        let currentAngle = 0;
        let isSpinning = false;

        // Colors for segments
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];

        // Draw the wheel
        function drawWheel() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const anglePerSegment = (2 * Math.PI) / gifts.length;

            gifts.forEach((gift, index) => {
                const startAngle = currentAngle + (index * anglePerSegment);
                const endAngle = startAngle + anglePerSegment;

                // Draw segment
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                ctx.closePath();
                ctx.fillStyle = colors[index % colors.length];
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Draw text
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(startAngle + anglePerSegment / 2);
                ctx.textAlign = 'center';
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(gift.name, radius * 0.7, 5);
                ctx.restore();
            });


            // Draw center circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
            ctx.fillStyle = '#333';
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();
        }

        // Initial draw
        drawWheel();

        // Spin functionality
        function spinWheel() {
            if (isSpinning) return;

            isSpinning = true;
            const button = document.getElementById('spin-button');
            button.disabled = true;
            button.textContent = '🎯 SPINNING...';

            // Random spin amount (3-8 full rotations plus random angle)
            const spins = 3 + Math.random() * 5;
            const finalAngle = spins * 2 * Math.PI + Math.random() * 2 * Math.PI;
            const startAngle = currentAngle;
            const startTime = Date.now();
            const duration = 3000; // 3 seconds

            function animate() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth deceleration
                const easeOut = 1 - Math.pow(1 - progress, 3);
                currentAngle = startAngle + finalAngle * easeOut;

                drawWheel();

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Determine winning segment
                    const normalizedAngle = (2 * Math.PI - (currentAngle % (2 * Math.PI))) % (2 * Math.PI);
                    const segmentAngle = (2 * Math.PI) / gifts.length;
                    const winningIndex = Math.floor(normalizedAngle / segmentAngle) % gifts.length;
                    const winningGift = gifts[winningIndex];

                    // Show result
                    setTimeout(() => {
                        alertPrize(winningGift);
                        isSpinning = false;
                    }, 500);
                }
            }

            animate();
        }

        // Spin button functionality
        document.getElementById('spin-button').addEventListener('click', spinWheel);

        // Prize alert function
        function alertPrize(gift) {
            document.getElementById('prize-name').textContent = gift.name;
            document.getElementById('result-section').classList.remove('hidden');
            document.getElementById('platforms-section').classList.remove('hidden');

            // Scroll to result
            document.getElementById('result-section').scrollIntoView({ behavior: 'smooth' });
        }

        })(); // Close IIFE

        // Track platform clicks - MOVED OUTSIDE IIFE so it's always available
        window.trackClick = function(platformId) {
        // Get platform URL immediately (client-side fallback)
        const platforms = @json($platforms);
        const platform = platforms.find(p => p.id === platformId);
        const url = platform?.url || '#';
        
        // Open the URL immediately (works better in Safari)
        const newWindow = window.open(url, '_blank');
        
        // Track the click in the background
        fetch(`{{ route('landing.click', $receptionist->unique_url) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                platform_id: platformId
            })
        }).then(response => {
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return response.json();
        }).then(data => {
            // If server returns a different URL and window is still open, redirect it
            if (data.success && data.platform_url && newWindow && !newWindow.closed) {
                newWindow.location.href = data.platform_url;
            }
        }).catch(error => {
            console.error('Error tracking click:', error);
        });
        }
    </script>
</body>
</html>
