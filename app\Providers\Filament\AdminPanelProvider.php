<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Pages\Auth\EditProfile;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        // Get settings with fallbacks
        $brandName = Setting::get('brand_name', 'Review Tracker');
        $brandLogo = Setting::get('brand_logo', 'assets/logos/logo.svg');
        $darkBrandLogo = Setting::get('dark_brand_logo', 'assets/logos/logo-dark.svg');
        $favicon = Setting::get('favicon', 'assets/icons/favicon.ico');
        $primaryColor = Setting::get('primary_color', '#92541B');
        $navigationType = Setting::get('navigation_type', 'sidebar');

        // Helper function to get correct asset URL
        $getAssetUrl = function($path) {
            if (empty($path)) return null;

            // If it's an uploaded file (starts with assets/), use Storage URL
            if (str_starts_with($path, 'assets/')) {
                return Storage::disk('public')->exists($path)
                    ? Storage::disk('public')->url($path)
                    : asset($path); // Fallback to public asset
            }

            return asset($path);
        };

        $panelConfig = $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->profile(EditProfile::class)
            ->brandName($brandName)
            ->favicon($getAssetUrl($favicon))
            ->brandLogoHeight('80px')
            ->colors([
                'primary' => $primaryColor,
            ]);

        // Add brand logos if they exist
        if ($brandLogo) {
            $panelConfig = $panelConfig->brandLogo($getAssetUrl($brandLogo));
        }

        if ($darkBrandLogo) {
            $panelConfig = $panelConfig->darkModeBrandLogo($getAssetUrl($darkBrandLogo));
        }

        // Configure navigation type
        if ($navigationType === 'top') {
            $panelConfig = $panelConfig->topNavigation();
        }

        return $panelConfig
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                //Widgets\AccountWidget::class,
                //Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
