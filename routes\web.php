<?php

use App\Http\Controllers\BadgeController;
use App\Http\Controllers\LandingController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Badge download route
Route::get('/receptionist/{receptionist}/badge', [BadgeController::class, 'downloadBadge'])
    ->name('receptionist.badge');

// Landing page routes
Route::get('/landing/{unique_url}', [LandingController::class, 'show'])
    ->name('landing.show');

Route::post('/landing/{unique_url}/click', [LandingController::class, 'trackClick'])
    ->name('landing.click');
